C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - BUFFER OVERRUN:
========================================
cvdump output shows:
(000038) S_GPROC32_ID: [0001:00000000], Cb: 0000001A, ID: 0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014
cbSymSeg: 117   cbRec: 7168     RecType: 0x1147
CVDUMP : fatal error : Overran end of symbol table

ANALYSIS:
========
✅ S_GPROC32_ID structure is CORRECT (names, offsets, types all perfect)
❌ CRITICAL: Symbol subsection size calculation is WRONG
- cbSymSeg: 117 (actual symbol data size)
- cbRec: 7168 (huge invalid record size)
- RecType: 0x1147 (invalid, reading garbage data)

ROOT CAUSE:
==========
The writeSubsection() method is writing incorrect subsection size,
causing cvdump to read beyond actual symbol data into garbage memory.

IMMEDIATE FIX REQUIRED:
======================
Fix subsection size calculation in writeSubsection() method to prevent
cvdump from reading beyond the actual symbol data boundary.


