C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - BUFFER OVERRUN:
========================================
cvdump output shows:
(000038) S_GPROC32_ID: [0001:00000000], Cb: 0000001A, ID: 0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014
cbSymSeg: 117   cbRec: 7168     RecType: 0x1147
CVDUMP : fatal error : Overran end of symbol table

ANALYSIS:
========
✅ S_GPROC32_ID structure is CORRECT (names, offsets, types all perfect)
❌ CRITICAL: Symbol subsection size calculation is WRONG
- cbSymSeg: 117 (actual symbol data size)
- cbRec: 7168 (huge invalid record size)
- RecType: 0x1147 (invalid, reading garbage data)

ROOT CAUSE:
==========
The writeSubsection() method is writing incorrect subsection size,
causing cvdump to read beyond actual symbol data into garbage memory.

PROGRESS UPDATE:
===============
✅ FIXED: S_GPROC32_ID structure is now CORRECT (names, offsets, types perfect)
✅ FIXED: Subsection size calculation uses LLVM format (no padding in length)
✅ FIXED: Symbol record flags field corrected (uint8_t instead of uint16_t)
✅ FIXED: Text section number now dynamic (3) instead of hardcoded (1)
✅ FIXED: Missing symbol types added (S_FRAMEPROC, S_LOCAL)
🎉 FIXED: CRITICAL PADDING ISSUE - removed padding from individual symbol records

MAJOR BREAKTHROUGH:
==================
Buffer overrun issue COMPLETELY RESOLVED! cvdump now parses:
✅ S_OBJNAME: Correct
✅ S_GPROC32_ID for "add": [0003:00000000] with correct segment
✅ S_FRAMEPROC: Parsed correctly
✅ S_PROC_ID_END: Parsed correctly
✅ S_GPROC32_ID for "main": [0003:00000020] with correct segment
✅ S_FRAMEPROC: Parsed correctly
✅ S_PROC_ID_END: Parsed correctly
✅ FILECHKSUMS: Parsed correctly

REMAINING ISSUES:
================
🎉 MAJOR BREAKTHROUGH: First function line information COMPLETELY WORKING!
✅ FIRST FUNCTION: fileid = 0x00 (correct!), line entry "3 00000013" (perfect!)
🎯 SECOND FUNCTION: fileid = 0xF2 (242) - still has 4-byte offset error

✅ FIXED: Line information structure - separate subsections per function
✅ FIXED: LineInfoHeader size (12 bytes) - was 16, now correct
✅ FIXED: First function line information parsing completely
Still need to fix remaining 4-byte offset in second function line parsing.

INCREDIBLE PROGRESS: Core line information generation is working correctly!


