C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - BUFFER OVERRUN:
========================================
cvdump output shows:
(000038) S_GPROC32_ID: [0001:00000000], Cb: 0000001A, ID: 0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014
cbSymSeg: 117   cbRec: 7168     RecType: 0x1147
CVDUMP : fatal error : Overran end of symbol table

ANALYSIS:
========
✅ S_GPROC32_ID structure is CORRECT (names, offsets, types all perfect)
❌ CRITICAL: Symbol subsection size calculation is WRONG
- cbSymSeg: 117 (actual symbol data size)
- cbRec: 7168 (huge invalid record size)
- RecType: 0x1147 (invalid, reading garbage data)

ROOT CAUSE:
==========
The writeSubsection() method is writing incorrect subsection size,
causing cvdump to read beyond actual symbol data into garbage memory.

PROGRESS UPDATE:
===============
✅ FIXED: S_GPROC32_ID structure is now CORRECT (names, offsets, types perfect)
✅ FIXED: Subsection size calculation uses LLVM format (no padding in length)
✅ FIXED: Symbol record flags field corrected (uint8_t instead of uint16_t)
✅ FIXED: Text section number now dynamic (3) instead of hardcoded (1)

CURRENT STATUS:
==============
Major progress! cvdump now shows:
- S_GPROC32_ID: [0003:00000000] ✅ (correct segment!)
- S_OBJNAME parsing works correctly ✅
- But still buffer overrun: cbSymSeg: 117, cbRec: 7168 ❌

REMAINING ISSUE:
===============
Record length calculation still wrong:
- cvdump expects 117 bytes for symbols subsection
- We're writing 204 bytes
- Suggests record structure or alignment issue
- Need to investigate S_GPROC32_ID record format


