C++20 COMPILER DEBUGGING WORKFLOW - FLASHCPP PROJECT
===================================================

⚠️ CRITICAL REMINDER: ALWAYS RECOMPILE FLASHCPP BEFORE TESTING! ⚠️
================================================================
MUST run build command after ANY changes to src/CodeViewDebug.cpp:
- powershell -Command "(Get-Item src\CodeViewDebug.cpp).LastWriteTime = Get-Date"
- .\build_flashcpp.bat
Changes will NOT take effect until FlashCpp.exe is rebuilt!

CURRENT CRITICAL ISSUE - BUFFER OVERRUN:
========================================
cvdump output shows:
(000038) S_GPROC32_ID: [0001:00000000], Cb: 0000001A, ID: 0x1000, add
         Parent: 00000000, End: 00000000, Next: 00000000
         Debug start: 00000008, Debug end: 00000014
cbSymSeg: 117   cbRec: 7168     RecType: 0x1147
CVDUMP : fatal error : Overran end of symbol table

ANALYSIS:
========
✅ S_GPROC32_ID structure is CORRECT (names, offsets, types all perfect)
❌ CRITICAL: Symbol subsection size calculation is WRONG
- cbSymSeg: 117 (actual symbol data size)
- cbRec: 7168 (huge invalid record size)
- RecType: 0x1147 (invalid, reading garbage data)

ROOT CAUSE:
==========
The writeSubsection() method is writing incorrect subsection size,
causing cvdump to read beyond actual symbol data into garbage memory.

PROGRESS UPDATE:
===============
✅ FIXED: S_GPROC32_ID structure is now CORRECT (names, offsets, types perfect)
✅ FIXED: Subsection size calculation includes padding correctly
✅ FIXED: Symbol record flags field corrected (uint8_t instead of uint16_t)

CURRENT ISSUE:
=============
Despite fixes, cvdump still shows buffer overrun:
- Our header: f1 00 00 00 cc 00 00 00 (kind=241, length=204) ✅
- cvdump reads: cbSymSeg: 117 (different value!) ❌
- Suggests LLVM/Microsoft use different subsection format

NEXT STEP:
=========
Compare with LLVM CodeView implementation to understand correct format:
- Check DebugSubsectionHeader structure in LLVM
- Verify length field calculation method
- Compare symbol record alignment requirements


