FlashCpp C++20 Compiler Debug Information Progress
=================================================

Date: 2025-06-14

Current Task: Compare cvdump output between test_debug.obj (FlashCpp generated) and test_debug_ref.obj (MSVC generated)

Test File: test_debug.cpp
- Simple C++ program with add() function and main()
- Tests basic function calls and debug information generation

Known Batch Files for Consistent Workflow:
==========================================

1. build_flashcpp_debug.bat - Build FlashCpp version of test_debug.obj
2. compile_ref.bat - Build MSVC reference version (test_debug_ref.obj)  
3. link_and_run_test_debug.bat - Link and run FlashCpp version
4. link_and_run_test_debug_ref.bat - Link and run MSVC reference version
5. dump_obj.bat - Dump object file contents with cvdump.exe

Current Status:
==============
- test_debug.obj exists (FlashCpp generated)
- test_debug_ref.obj exists (MSVC reference)
- test_debug_cvdump.txt exists but shows "CVDUMP : fatal error : Invalid file"
- test_debug_ref_cvdump.txt exists and shows proper debug information

Issues Found:
============
1. FlashCpp generated obj file appears to have invalid debug information format
2. MSVC reference shows proper CodeView debug symbols including:
   - S_OBJNAME, S_COMPILE3 records
   - Function symbols (S_GPROC32_ID) for add() and main()
   - Line number information
   - Parameter information (S_REGREL32)

CVDUMP COMPARISON RESULTS (2025-06-14):
=======================================

FlashCpp Output (test_debug_cvdump_new.txt):
- ERROR: "CVDUMP : fatal error : Overran end of symbol table"
- Shows S_OBJNAME records but corrupted symbol table
- cbSymSeg: 125, cbRec: 6400, RecType: 0x1101 (invalid)
- Debug information structure is malformed

MSVC Reference Output (test_debug_ref_cvdump_new.txt):
- SUCCESS: Complete and valid debug information
- Proper S_OBJNAME, S_COMPILE3 records
- Function symbols: S_GPROC32_ID for add() and main()
- Parameter information: S_REGREL32 for function arguments
- Line number information properly formatted
- File checksums and string table present

KEY DIFFERENCES IDENTIFIED:
==========================
1. FlashCpp symbol table is corrupted/oversized
2. Missing S_COMPILE3 record in FlashCpp output
3. No function symbols (S_GPROC32_ID) in FlashCpp
4. No parameter information (S_REGREL32) in FlashCpp
5. No line number information in FlashCpp
6. No file checksums or string table in FlashCpp

MAJOR BREAKTHROUGH - DUPLICATE S_OBJNAME FIXED! 🎉
=================================================
✅ FIXED: S_OBJNAME now uses correct source file path (test_debug.obj)
✅ FIXED: .debug$S is now first section for easier comparison
✅ FIXED: Only ONE S_OBJNAME record generated (no more duplicates!)

CURRENT STATUS:
==============
- cvdump shows: "(00000C) S_OBJNAME: Signature: 00000000, test_debug.ob"
- Only one S_OBJNAME record (duplicate issue RESOLVED!)
- Still getting "Overran end of symbol table" error

REMAINING CRITICAL ISSUE:
========================
❌ CRITICAL: Symbol table size calculation incorrect
- Subsection header claims more symbols exist than actually generated
- Only S_OBJNAME is generated, but size suggests more symbols
- Need to fix subsection size calculation in generateDebugS()

COMPARISON WITH REFERENCE:
=========================
Reference (MSVC): S_OBJNAME + S_COMPILE3 + S_GPROC32_ID + S_FRAMEPROC + etc.
FlashCpp: Only S_OBJNAME (missing all other symbols)

NEXT STEPS:
==========
1. Fix subsection size calculation to match actual symbol data
2. Re-enable function symbol generation (S_GPROC32_ID, S_FRAMEPROC)
3. Add missing symbols (S_COMPILE3, S_REGREL32, etc.)

IMMEDIATE FIX REQUIRED:
======================
1. ✓ Uncomment the function symbol generation code (lines 452-600)
2. ✓ Re-enable line information generation (lines 612-616)
3. ✓ Test with cvdump to verify proper debug structure

NEW ISSUE DISCOVERED:
====================
From debug output: "DEBUG: Number of functions: 0"
- Functions are not being added to the debug builder
- This explains why no function symbols are generated
- Only S_OBJNAME records are created, causing symbol table corruption
- Linker warning: "debugging information corrupt; recompile module"

ROOT CAUSE: Functions not being registered with debug builder
The IRConverter is not calling the debug builder's addFunction() method

FUNCTION REGISTRATION FIX APPLIED:
==================================
1. ✓ Added function registration in handleFunctionDecl() (IRConverter.h:922)
2. ✓ Added updateFunctionLength() method to debug builder
3. ✓ Updated handleFunctionEnd() to call update_function_length()
4. ✓ Functions should now be registered before line mappings

REMAINING ISSUE:
===============
- Still getting duplicate S_OBJNAME records in cvdump output
- "CVDUMP : fatal error : Overran end of symbol table"
- Two S_OBJNAME records at offsets 00000C and 000021
- This suggests S_OBJNAME is being written twice somewhere

CURRENT STATUS (After Function Registration Fix):
===============================================
1. ✓ Enable function symbol generation in CodeViewDebug.cpp
2. ✓ Test incremental fixes with cvdump validation
3. ✓ Fix function registration in IRConverter
4. ✓ Rebuild FlashCpp compiler with all changes
5. ❌ CRITICAL: Still getting duplicate S_OBJNAME generation

PERSISTENT ISSUE:
================
- Still getting "CVDUMP : fatal error : Overran end of symbol table"
- Two S_OBJNAME records at offsets 00000C and 000021
- cbSymSeg: 125, cbRec: 6400, RecType: 0x1101 (invalid)
- Linker warning: "debugging information corrupt; recompile module"

ROOT CAUSE ANALYSIS NEEDED:
==========================
The duplicate S_OBJNAME suggests either:
1. S_OBJNAME is being written twice in generateDebugS()
2. Debug section is being generated multiple times
3. Symbol table structure is fundamentally incorrect
4. Length calculations are wrong causing parser confusion

Next Steps:
==========
1. ✓ Enable function symbol generation in CodeViewDebug.cpp
2. ✓ Test incremental fixes with cvdump validation
3. ✓ Fix function registration in IRConverter
4. ❌ CRITICAL: Debug duplicate S_OBJNAME generation
5. Add debug output to track symbol generation
6. Compare symbol table structure with MSVC reference

Workflow Commands:
=================
- Build FlashCpp obj: build_flashcpp_debug.bat
- Build MSVC ref: compile_ref.bat
- Compare cvdump: cvdump_compare.bat
- Link FlashCpp: link_and_run_test_debug.bat
- Link MSVC: link_and_run_test_debug_ref.bat
